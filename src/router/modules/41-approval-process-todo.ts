import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: 'process-todo',
        name: 'ProcessToDo',
        meta: {
            title: '审批待办',
            icon: 'menus-process6',
            isFullLayout: true,
            hidden: true,
            permissions: [
                { name: '审批待办处理', code: 'processToDoHandle' },
                { name: '审批待办查看', code: 'processToDoView' },
                { name: '审批待办撤回', code: 'processToDoRecall' }
            ]
        },
        component: () => import('@/views/approval/process-todo.vue')
    },
];
export default routes;

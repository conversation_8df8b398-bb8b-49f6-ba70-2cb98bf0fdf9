<template>
    <n-popselect
        @update:value="valueChange"
        v-model:value="selectedValue"
        :options="options"
        :render-label="renderLabel"
        trigger="click"
        scrollable
    >
        <div class="flex justify-center items-center cursor-pointer text-12px">
            <span class="flex-1 text-center text-ellipsis whitespace-nowrap overflow-hidden">
                {{ selectedLabel }}
            </span>
            <span class="c-var-primary-color ml-5px text-10px">切换</span>
        </div>
    </n-popselect>
</template>

<script lang="ts" setup>
import { NEllipsis, SelectGroupOption, SelectOption } from 'naive-ui';
import { setRoutes } from '@/router/set-routes';
import { useRouter } from 'vue-router';
import useStore from '@/store/modules/main';

const store = useStore();
const router = useRouter();
const options = ref<SelectOption[]>([]);
const selectedValue = ref(store.userInfo.currentOrganization || '');

const selectedLabel = computed(() => {
    const found = options.value.find((v: any) => v.value === selectedValue.value);
    return found ? found.label : options.value[0]?.label || '暂未分配';
});

const init = async () => {
    const resData = await api.sass.api.v1.user.get_group_companies();
    if (resData.data && resData.data && resData.data.length > 0) {
        options.value = resData.data;
    } else {
        options.value = [{ label: '暂未分配', value: 'not_yet_assigned' }];
        selectedValue.value = 'not_yet_assigned';
    }
};

const renderLabel = (option: SelectOption | SelectGroupOption) => {
    return h(NEllipsis, { tooltip: { placement: 'left' }, style: { width: '150px' } }, () =>
        h('span', {}, { default: () => option.label })
    );
};

const valueChange = async (value: string) => {
    // 已选中在次选中不在调用切换逻辑
    if (value === selectedValue.value) return;
    const res = await api.sass.api.v1.user.update_organization({ organizationId: value });
    await store.setToken(res.data.accessToken);
    const userInfo = await api.sass.api.v1.user.info();
    await store.setUserInfo(userInfo.data);
    await setRoutes();
    await router.push('/');
};

onMounted(() => {
    init();
});
</script>

<style lang="less" scoped></style>

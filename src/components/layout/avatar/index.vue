<template>
    <div class="vaw-avatar-container">
        <n-dropdown
            class="vaw-avatar-container-dropdown"
            :options="options"
            trigger="click"
            size="large"
            @select="handleSelect"
        >
            <div class="action-wrapper">
                <div class="avatar">
                    <n-avatar
                        circle
                        size="small"
                        :src="store.userInfo.avatar?.url || defaultAvatar"
                        @error="avatarError"
                    />
                </div>
            </div>
        </n-dropdown>
        <debug-page ref="debugPageRef"></debug-page>
    </div>
</template>

<script lang="ts" setup>
import { NAvatar, NIcon, NText } from 'naive-ui';
import { LogInOutline, LockClosedOutline, Help, NewspaperOutline, ReaderOutline, Medical } from '@vicons/ionicons5';
import { useRouter } from 'vue-router';
import useStore from '@/store/modules/main';
import defaultAvatar from '@/assets/images/avatar.png';

const store = useStore();
const router = useRouter();

const options = computed(() => [
    {
        key: 'header',
        type: 'render',
        render: renderCustomHeader
    },
    {
        key: 'header-divider',
        type: 'divider'
    },
    {
        label: '密码修改',
        key: 'passwordUpdate',
        icon: () =>
            h(NIcon, null, {
                default: () => h(LockClosedOutline)
            })
    },
    {
        label: '签名管理',
        key: 'signature',
        icon: () =>
            h(NIcon, null, {
                default: () => h(NewspaperOutline)
            }),
        show: !!store.flatRoutes.find((route) => route.name === 'Signature')
    },
    {
        label: '数据导出',
        key: 'dataExport',
        icon: () =>
            h(NIcon, null, {
                default: () => h(ReaderOutline)
            })
    },
    {
        label: '更多帮助',
        key: 'moreHelp',
        icon: () =>
            h(NIcon, null, {
                default: () => h(Help)
            })
    },
    {
        key: 'header-divider',
        type: 'divider'
    },
    {
        label: '退出登录',
        type: 'render',
        render: renderLogout
    },
    {
        key: 'header-divider',
        type: 'divider',
        show: store.userInfo.isAdmin === true
    },
    {
        label: '调试工具(管理员)',
        key: 'debug',
        icon: () =>
            h(NIcon, null, {
                default: () => h(Medical)
            }),
        show: store.userInfo.isAdmin === true
    }
]);

const renderCustomHeader = () => {
    return h(
        'div',
        {
            style: 'display: flex; align-items: center; padding: 8px 12px;'
        },
        [
            h(NAvatar, {
                round: true,
                class:'mr-12px w-'
                style: 'margin-right: 12px;',
                src: store.userInfo.avatar?.url || defaultAvatar
            }),
            h('div', null, [
                h('div', null, [h(NText, { depth: 2 }, { default: () => store.userInfo.nickname || '管理员' })]),
                h(NText, { depth: 3, class: 'c-#909090 text-12px' }, [
                    h(
                        defineAsyncComponent(() => import('@/components/business/change-company-model.vue')),
                        {}
                    )
                ])
            ])
        ]
    );
};

const renderLogout = () => {
    return h(
        'div',
        {
            class: 'flex items-center px-10px py-8px cursor-pointer',
            onClick: () => {
                logout();
            }
        },
        [
            h(
                NIcon,
                { size: 17, color: '#d03050' },
                {
                    default: () => h(LogInOutline)
                }
            ),
            h(NText, { depth: 2, type: 'error', class: 'text-15px ml-12px ' }, { default: () => '退出登录' })
        ]
    );
};

const avatarError = () => {
    store.removeUserInfoAvatar();
};

const logout = () => {
    window.$dialog.warning({
        title: '提示',
        content: '是否要退出当前账号？',
        positiveText: '退出',
        negativeText: '再想想',
        onPositiveClick: () => {
            router.replace({ name: 'login' });
        }
    });
};

const helpDialog = () => {
    $alert.dialog({
        title: '更多帮助',
        width: '900px',
        content: import('@/views/system/more-help/models/mini-help-list.vue')
    });
};
const dataExport = () => {
    $alert.dialog({
        title: '数据导出',
        width: '900px',
        content: import('@/views/system/data-export/index.vue')
    });
};

const handlePasswordUpdate = () => {
    $alert.dialog({
        title: '密码修改',
        width: '500px',
        content: import('@/views/system/organization/components/models/update-password.vue'),
        props: {
            ids: store.userInfo.id ? [store.userInfo.id] : [],
            onSave: () => updateSuccess()
        }
    });
};

const handleDebug = () => {
    debugPageRef.value.open();
};

function handleSelect(key: string) {
    switch (key) {
        case 'logout':
            logout();
            break;
        case 'moreHelp':
            helpDialog();
            break;
        case 'dataExport':
            dataExport();
            break;
        case 'passwordUpdate':
            handlePasswordUpdate();
            break;
        case 'signature':
            router.push({ name: 'Signature' });
            break;
        case 'debug':
            handleDebug();
            break;
    }
}

const updateSuccess = () => {
    router.replace({ name: 'login' });
};

const debugPageRef = ref();
</script>

<style lang="less" scoped>
.vaw-avatar-container {
    .action-wrapper {
        display: flex;
        align-items: center;

        .avatar {
            width: calc(var(--logo-height)- 15px);
            height: calc(var(--logo-height) - 15px);
            display: flex;
            align-items: center;

            & > img {
                border: 1px solid #f6f6f6;
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 50%;
            }
        }
    }
}

.vaw-avatar-container:hover {
    cursor: pointer;
}
</style>

<template>
    <div v-if="$router.hasRoute('ProcessToDo')" class="notice-container" @click="handleProcessToDo">
        <span>待办事项</span>
        <n-badge :value="todoCount" :max="99" :offset="[35, -10]"> </n-badge>
    </div>
</template>

<script setup lang="ts">
const router = useRouter();

const todoCount = ref();
const handleProcessToDo = () => {
    router.push('/process-todo');
};

const getMsgTotal = async () => {
    const res = await api.sass.api.v1.workflow.task.list.todos({ page: 1, pageSize: 10 });
    todoCount.value = res.data.total;
};

// 获取消息数据
onMounted(async () => {
    await getMsgTotal();
});
</script>

<style scoped lang="less">
.notice-container {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background-color: rgba(255, 255, 255, 0.8);
    }
}

:deep(.n-badge) {
    position: absolute;
    .n-badge-sup {
        font-size: 11px;
        height: 14px;
        padding: 0 3px;
        .n-base-slot-machine {
            height: 12px;
            line-height: 12px;
        }
    }
}
</style>

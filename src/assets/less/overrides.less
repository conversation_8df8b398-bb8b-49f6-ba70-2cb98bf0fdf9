:root {
    --tab-split-menu-width: 65px;
    --menu-width: 240px;
    --min-menu-width: 60px;
    --transition-time: 0.3s ease;
    --logo-height: 60px;
    --tab-height: 40px;
    --footer-height: 45px;
    --h5-bottom-nav-height: 50px;
    --primary-color: #005eff;
}

/**
 * 中一检测TTB模式下，顶部logo、nav、menu样式配置
 */
.zy-scroller-menu {
    .n-scrollbar-content {
        height: 100% !important;
        .n-menu {
            height: 100% !important;

            .n-menu-item {
                height: 100% !important;
                --n-item-text-color: #fff;
                --n-item-text-color-hover-horizontal: #fff;
                --n-item-text-color-active-horizontal: #fff;
                --n-item-text-color-active-hover-horizontal: #fff;

                .n-menu-item-content__icon {
                    display: none !important;
                }
                .n-menu-item-content-header {
                    padding: 0 !important;
                    font-size: 16px !important;
                    // 不省略文本
                    text-overflow: clip !important;
                    white-space: nowrap !important;
                    overflow: visible !important;
                }
            }
        }
    }
}

// 首页路由
.home-top-menu-scrollbar {
    .n-scrollbar-content {
        height: 100% !important;
        .n-menu {
            height: 100% !important;
            .n-menu-item {
                height: 100% !important;

                .n-menu-item-content-header {
                    padding: 0 !important;
                    font-size: 16px !important;
                    // 不省略文本
                    text-overflow: clip !important;
                    white-space: nowrap !important;
                    overflow: visible !important;
                }
            }
        }
    }
}

.signature-preview-modal.n-modal {
    .n-card-header {
        .n-base-close {
            color: #fff;
            border-radius: 100%;
            font-size: 13px;
            padding: 11px;
            background: linear-gradient(272deg, #4a9bff 6%, #005eff 98%);
            &:hover {
                background: linear-gradient(0deg, #4a9bff 6%, #005eff 98%);
                color: #fff !important;
            }
        }
    }
}

/**
 * 头像下拉菜单
 */
.vaw-avatar-container-dropdown.n-dropdown-menu {
    width: 220px;
}

/* 自定义滚动条轨道 */
::-webkit-scrollbar {
    width: 5px; /* 滚动条宽度 */
    height: 5px; /* 滚动条高度 */
}

/* 自定义滚动条滑块 */
::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.25); /* 滑块背景颜色 */
    border-radius: 5px; /* 滑块圆角 */
}

/* 自定义滚动条轨道背景 */
::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.02); /* 轨道背景颜色 */
    border-radius: 5px; /* 轨道圆角 */
}

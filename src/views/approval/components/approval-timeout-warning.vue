<template>
    <div class="approval-timeout-warning w-100% flex-center p-x-15px gap-10px h-$n-height">
        <bsRadio v-model="modelValue" :options="options" :disabled="disabled"></bsRadio>
        <n-input-number
            :readonly="!modelValue || disabled"
            class="flex-1"
            v-model:value="formData[keyName]"
            :min="1"
            clearable
        >
            <template #suffix>小时</template>
        </n-input-number>
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        formData?: any;
        keyName?: any;
        disabled?: boolean;
    }>(),
    {
        keyName: 'value',
        disabled: false
    }
);
const emit = defineEmits(['update:modelValue', 'update:formData']);
const { modelValue, formData } = useVModels(props, emit);

// 监听bsRadio的变化，当选择"开启"时设置数值为1
watch(modelValue, (newValue) => {
    if (newValue === true && formData.value && props.keyName) {
        formData.value[props.keyName] = 1;
    } else {
        formData.value[props.keyName] = 0;
    }
});

const options = ref([
    {
        label: '开启',
        value: true
    },
    {
        label: '关闭',
        value: false
    }
]);
</script>
<style scoped lang="less"></style>

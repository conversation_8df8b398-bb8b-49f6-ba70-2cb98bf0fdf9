<template>
    <alert-content :on-default-save="submit">
        {{ selected }}
        <n-transfer-tree
            ref="transferTree"
            v-model:value="selected"
            :options="users"
            :tree-props="{
                style: 'margin: 0 4px;',
                keyField: 'key',
                labelField: 'label',
                checkable: true,
                selectable: false,
                blockLine: true,
                checkOnClick: true,
                cascade: true,
                checkStrategy: 'child'
            }"
            :transfer-props="{
                sourceFilterable: true
            }"
        />
    </alert-content>
</template>

<script lang="ts" setup>
import { UserListData } from '@/api/sass/api/v1/user';

const props = withDefaults(
    defineProps<{
        id: string;
    }>(),
    {
        id: ''
    }
);

const selected = ref<string[]>(['570845193729765014']);
const rId = ref('');
const users = ref<UserListData[]>([]);
const componentKey = ref(0);

// watch(
//     users,
//     (newUsers) => {
//         if (newUsers.length > 0 && selected.value.length > 0) {
//             componentKey.value++;
//         }
//     },
//     { deep: true }
// );

const open = async (id: string) => {
    users.value = [];
    rId.value = id;
    init();
};

const init = async () => {
    const res = await window.api.sass.api.v1.role.getOrganizationUserListTree({
        descendantNodeTypes: [2]
    });
    console.log('🚀 ~ init ~ res:', res);

    const convertToTreeFormat = (nodes: any[]): any[] => {
        return nodes.map((node) => {
            const result: any = {
                key: node.orgId,
                label: node.orgName,
                parentId: node.parentId,
                isUser: false,
                children: []
            };

            if (node.children && Array.isArray(node.children)) {
                const childOrgs = convertToTreeFormat(node.children);
                result.children = result.children.concat(childOrgs);
            }

            if (node.userInfo && Array.isArray(node.userInfo)) {
                node.userInfo.forEach((user: any) => {
                    result.children.push({
                        key: user.userId,
                        label: user.nickname,
                        parentId: node.orgId,
                        isUser: true,
                        children: null
                    });
                });
            }

            if (result.children.length === 0) {
                result.children = [];
            }

            return result;
        });
    };

    users.value = await convertToTreeFormat(res.data);
    console.log('🚀 ~ init ~ users.value:', users.value);
    nextTick(async () => {
        const selectedUsersRes = await window.api.sass.api.v1.role.users.get(props.id);
        const selectedUsers = selectedUsersRes.data.data;
        selected.value = selectedUsers.map((v: any) => v.id);
        console.log('🚀 ~ init ~ selected.value:', selected.value);
    });
};

const submit = async () => {
    const res = await window.api.sass.api.v1.role.users.update_users(rId.value, selected.value);
    window.$message.success(res.msg as string);
};

onMounted(() => {
    open(props.id);
});
</script>

<style scoped></style>

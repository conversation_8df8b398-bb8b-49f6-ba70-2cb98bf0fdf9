<template>
    <div class="home flex-v justify-around">
        <div class="main mt-10vh">
            <div class="hello flex-v">
                <span class="text-58px bold">
                    <type-writer
                        ref="typewriterRef"
                        :texts="welcomeTexts"
                        :type-speed="200"
                        :back-speed="100"
                        :back-delay="2000"
                        :loop="false"
                        text-type="default"
                        cursor-char="| "
                        :cursor-blink="true"
                        :session-cache="{
                            enabled: true,
                            key: 'home-message',
                            showLastText: true
                        }"
                    />
                </span>
                <n-input class="w-620px! h-60px! mt-24px flex items-center text-18px" placeholder="输入要搜索的内容">
                    <template #prefix>
                        <n-icon size="24" :component="SearchOutline" />
                    </template>
                </n-input>
            </div>
        </div>
        <div class="notice flex mt-10vh">
            <div
                class="pending w-366px h-72px flex-center-between bg-[linear-gradient(270deg,_#4A9BFF_3%,_#005EFF_99%)] text-20px bold c-#fff px-32px cursor-pointer"
                @click="$router.push('/process-todo')"
            >
                <span>待办事项</span>
                <n-icon size="24" :component="ArrowForwardOutline" />
            </div>
            <div
                class="message w-238px h-72px flex-center text-18px bold c-#005EFF border b-solid border-[#829CCA] box-border backdrop-blur-10px ml-16px cursor-pointer"
                @click="handleNotice()"
            >
                <span>消息通知</span>
            </div>
        </div>
        <div
            class="cards mt-10vh rounded-6px flex flex-wrap box-border b-1px b-solid b-[#fff] bg-[linear-gradient(311deg,_#EAF3FF_19%,_rgba(255,255,255,0.3)_41%)] backdrop-blur-5px"
        >
            <div
                v-for="(item, idx) in cards"
                :key="idx"
                class="card w-125px h-125px my-20px mx-18px flex-center flex-col rounded-4px box-border b-1px b-solid b-[#fff] backdrop-blur-10px bg-[linear-gradient(339deg,_rgba(255,255,255,0.9)_9%,_rgba(255,255,255,0)_92%)] cursor-pointer"
            >
                <n-image :src="item.icon" class="w-52px h-52px mb-12px" preview-disabled />
                <div v-if="item.label" class="text-16px font-500 c-#333 mt-4px">{{ item.label }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import { SearchOutline, ArrowForwardOutline } from '@vicons/ionicons5';
import add from '@/assets/images/entry/add.webp';
import card1 from '@/assets/images/entry/card1.webp';
import card2 from '@/assets/images/entry/card2.webp';
import card3 from '@/assets/images/entry/card3.webp';
import card4 from '@/assets/images/entry/card4.webp';
import card5 from '@/assets/images/entry/card5.webp';
import card6 from '@/assets/images/entry/card6.webp';
import card7 from '@/assets/images/entry/card7.webp';
import card8 from '@/assets/images/entry/card8.webp';
import card9 from '@/assets/images/entry/card9.webp';
import card10 from '@/assets/images/entry/card10.webp';

const store = useStore();

const cards = ref([
    { label: '消息通知', icon: card1 },
    { label: '培训履历', icon: card2 },
    { label: '客户分析', icon: card3 },
    { label: '人事管理', icon: card4 },
    { label: '采购管理', icon: card5 },
    { label: '薪酬管理', icon: card6 },
    { label: '机构管理', icon: card7 },
    { label: '假勤管理', icon: card8 },
    { label: '任务委托', icon: card9 },
    { label: '任务分配', icon: card10 },
    { label: '', icon: add } // 最后一个加号
]);

const welcomeTexts = [
    `Hi, ${store.userInfo.nickname || ''}`,
    '欢迎使用本系统！',
    `Hi, ${store.userInfo.nickname || ''}`
];

const handleNotice = () => {
    $alert.dialog({
        title: '消息中心',
        width: '80%',
        content: import('@/components/business/notice-model.vue')
    });
};
</script>

<style lang="less" scoped>
.home {
    :deep(.n-input) {
        background: linear-gradient(304deg, #eaf3ff 19%, rgba(255, 255, 255, 0.3) 36%);
        box-sizing: border-box;
        backdrop-filter: blur(5px);
    }
    .notice {
        .pending:hover {
            background-position: right center;
            background-size: 200% auto;
            -webkit-animation: pulse 2s infinite;
            animation: pulse512 1.5s infinite;
            .n-icon {
                animation: moveRight 1.5s ease-in-out infinite;
            }
        }
        .message {
            border: 1px solid;
            overflow: hidden;
            position: relative;
            &:after {
                background: #90b1f7;
                content: '';
                height: 155px;
                left: -75px;
                opacity: 0.4;
                position: absolute;
                top: -50px;
                transform: rotate(35deg);
                transition: all 550ms cubic-bezier(0.19, 1, 0.22, 1);
                width: 50px;
                z-index: -10;
            }
            &:hover::after {
                left: 120%;
                transition: all 550ms cubic-bezier(0.19, 1, 0.22, 1);
            }
        }
    }
    .cards {
        .card {
            transition: all 0.4s cubic-bezier(0.15, 0.83, 0.66, 1);
            box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
        }
        .card:hover {
            transform: scale(1.2);
        }
    }
}

@keyframes moveRight {
    0% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(20px);
    }
    100% {
        transform: translateX(0);
    }
}
@keyframes rotate624 {
    0% {
        transform: rotate(0deg) translate3d(0, 0, 0);
    }

    25% {
        transform: rotate(3deg) translate3d(0, 0, 0);
    }

    50% {
        transform: rotate(-3deg) translate3d(0, 0, 0);
    }

    75% {
        transform: rotate(1deg) translate3d(0, 0, 0);
    }

    100% {
        transform: rotate(0deg) translate3d(0, 0, 0);
    }
}
@keyframes pulse512 {
    0% {
        box-shadow: 0 0 0 0 #3776ff;
    }

    70% {
        box-shadow: 0 0 0 10px rgb(218 103 68 / 0%);
    }

    100% {
        box-shadow: 0 0 0 0 rgb(218 103 68 / 0%);
    }
}
</style>

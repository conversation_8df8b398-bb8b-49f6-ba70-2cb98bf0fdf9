<template>
    <alert-content :on-default-save="handleSubmit">
        <n-form
            class="flex-v gap-10px mt-10px"
            :model="form"
            label-placement="left"
            :label-width="90"
            :show-feedback="false"
        >
            <n-form-item label="原文件编号">
                <n-input v-model:value="form.originalNumber" placeholder="原文件编号" maxlength="50" show-count />
            </n-form-item>

            <n-form-item label="原版本/版次">
                <n-input v-model:value="form.originalVersion" placeholder="原版本/版次" maxlength="50" show-count />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';

const form = ref({
    originalNumber: '',
    originalVersion: ''
});

const store = useStore();

const props = defineProps<{
    row: any;
}>();

const handleSubmit = async () => {
    const formContent = JSON.stringify({
        businessId: 'FILE_INCORPORATE',
        version: '1.0.0',
        data: {
            data: [
                {
                    fileName: props.row.name,
                    fileNo: props.row.number,
                    id: props.row.id,
                    originalNumber: form.value.originalNumber,
                    originalVersion: form.value.originalVersion
                }
            ],
            nickname: store.userInfo.nickname,
            applyDate: dayjs().format('YYYY-MM-DD')
            // reason: '纳入公司流程'
        }
    });
    await new Promise((resolve) => {
        window.$dialog.warning({
            title: '确认提交',
            content: '确认后将发起审批，是否确认提交？',
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                const res = await $apis.nebula.api.v1.external.plagiarismCheck({
                    ids: [props.row.id]
                });
                if (res.code === 0) {
                    await $hooks.useApprovalProcess('FILE_INCORPORATE', formContent);
                    window.$message.success('提交成功，审批通过后将纳入子公司库');
                    resolve(true);
                } else {
                    window.$message.error(res.message || '提交失败');
                    resolve(false);
                }
            }
        });
    });
};
</script>

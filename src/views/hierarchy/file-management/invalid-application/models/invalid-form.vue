<template>
    <alert-content :buttons="buttons">
        <n-form
            class="mt-2"
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="110"
            :show-feedback="false"
            :disabled="isPreview"
        >
            <n-grid :cols="24" :y-gap="12" :x-gap="16">
                <n-form-item-gi label="申请人" :span="12">
                    <n-input v-model:value="formData.applicant" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="申请日期" :span="12">
                    <n-input :value="dayjs(formData.applyDate).format('YYYY-MM-DD')" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="拟定作废日期" path="wishInvalidDate" :span="24">
                    <n-date-picker
                        v-model:value="formData.wishInvalidDate"
                        type="date"
                        placeholder="请选择拟定作废日期"
                        clearable
                        format="yyyy-MM-dd"
                        style="width: 100%"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" path="fileType" :span="12">
                    <n-select
                        v-model:value="formData.fileType"
                        :options="$datas.invalidFile.fileTypeOptions"
                        placeholder="请选择文件类型"
                        clearable
                        @update:value="onFileTypeChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" path="documentCategoryId" :span="12">
                    <SelectTreeDictionary
                        v-model:value="formData.documentCategoryId"
                        placeholder="请选择文件类别"
                        clearable
                        filterable
                        :disabled="!formData.fileType"
                        :need-path-info="true"
                        @change="onTypeDictNodeIdChange"
                        :params="formData.fileType === 1 ? 'internal' : formData.fileType === 2 ? 'external' : ''"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="作废原因" path="reason" :span="12">
                    <n-select
                        v-model:value="formData.reason"
                        :options="$datas.invalidFile.reasonOptions"
                        placeholder="请选择作废原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="formData.reason === 9" :span="24" label="其他原因" path="otherReason">
                    <n-input
                        v-model:value="formData.otherReason"
                        maxlength="50"
                        placeholder="请输入其他原因"
                        show-count
                    />
                </n-form-item-gi>

                <n-form-item-gi :span="24" path="distributeList">
                    <div class="flex-v w-100%">
                        <div class="flex justify-between">
                            <span class="ml-32px mb-10px required-field">发放清单</span>
                            <n-button
                                type="primary"
                                size="tiny"
                                :disabled="!formData.documentCategoryId || isPreview"
                                @click="handleAddFile"
                            >
                                增加文件
                            </n-button>
                        </div>
                        <vxe-table
                            ref="tableRef"
                            class="w-100%"
                            :border="true"
                            show-overflow
                            auto-resize
                            :edit-rules="validRules"
                            :valid-config="{ showMessage: false }"
                            :edit-config="{
                                trigger: 'click',
                                mode: 'cell',
                                enabled: !isPreview
                            }"
                            :data="formData.documents"
                        >
                            <vxe-column type="seq" title="序号" width="70" fixed="left"></vxe-column>
                            <vxe-column
                                field="documentId"
                                title="文件名称"
                                minWidth="150"
                                :edit-render="{}"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'documentIdOptions', row.documentId)"
                            >
                                <template #edit="{ row }">
                                    <n-select
                                        v-model:value="row.documentId"
                                        :options="documentIdOptions"
                                        @update:value="handleDocumentIdChange(row)"
                                        filterable
                                        clearable
                                        placeholder="可输入查询选项后选择"
                                        style="width: 100%"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentNo"
                                title="文件编号"
                                minWidth="150"
                                :edit-render="{}"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'documentNoOptions', row.documentNo)"
                            >
                                <template #edit="{ row }">
                                    <n-select
                                        v-model:value="row.documentNo"
                                        :options="documentNoOptions"
                                        @update:value="handleDocumentNoChange(row)"
                                        filterable
                                        clearable
                                        placeholder="可输入查询选项后选择"
                                        style="width: 100%"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column field="documentVersionNo" title="版本/版次" minWidth="120"> </vxe-column>
                            <vxe-column field="todo" title="操作" width="80" fixed="right">
                                <template v-slot="{ row }">
                                    <n-button v-if="!isPreview" size="tiny" type="error" @click="handleRemoveFile(row)">
                                        删除
                                    </n-button>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormRules } from 'naive-ui';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import type { ButtonsConfig } from '@/components/alert-content.vue';
import { RowVO } from '@/api/sass/api/v1/dict';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';

const props = defineProps<{
    row: any;
    isPreview: boolean;
}>();

const store = useStore();

const buttons = computed<ButtonsConfig>(() => {
    if (props.isPreview) {
        // 详情模式只显示关闭按钮
        return {};
    }

    return {
        save: {
            text: '提交',
            async onClick() {
                await onSubmit();
            }
        },
        extra: {
            saveTemp: {
                text: '暂存',
                type: 'success',
                onClick: async () => {
                    await onSubmit('saveTemp');
                },
                emit: 'saveTemp',
                autoClose: true
            }
        }
    };
});

const formData = reactive({
    applicant: store.userInfo.nickname,
    applyDate: dayjs().valueOf(),
    wishInvalidDate: null,
    fileType: null,
    documentCategoryId: null,
    reason: null,
    otherReason: null,
    documents: [] as any
});

const rules = computed<FormRules>(() => {
    return {
        fileType: { required: true, type: 'number', message: '请选择文件类型', trigger: 'change' },
        wishInvalidDate: { required: true, message: '请选择拟定作废日期', trigger: 'change', type: 'number' },
        documentCategoryId: { required: true, message: '请选择文件类别', trigger: 'change' },
        reason: { required: true, message: '请选择作废原因', trigger: 'change', type: 'number' },
        otherReason: { required: formData.reason === '其他', message: '请输入其他原因', trigger: 'blur' },
        distributeList: [
            {
                required: true,
                validator: () => {
                    if (!formData.documents.length) {
                        return new Error('请填写作废清单');
                    }
                    return true;
                }
            }
        ]
    };
});

// 文件列表数据
const fileList = ref<any[]>([]);
const fetchFileList = async () => {
    if (!formData.fileType || !formData.documentCategoryId) {
        fileList.value = [];
        return;
    }

    try {
        let list = [];
        if (formData.fileType === 1) {
            // 内部文件
            const res = await $apis.nebula.api.v1.internal.list({
                docCategoryIds: [formData.documentCategoryId],
                noPage: true
            });
            list = res.data?.data ?? [];
        } else if (formData.fileType === 2) {
            // 外部文件
            const orgType = store.userInfo.organizationType === 0 ? 1 : 2;
            const res = await $apis.nebula.api.v1.external.getList({
                typeDictionaryNodeIds: [formData.documentCategoryId],
                orgType,
                noPage: true
            });
            list = res.data?.data ?? [];
        }
        fileList.value = Array.isArray(list) ? list : [];
    } catch (error) {
        console.error('获取文件列表失败:', error);
        fileList.value = [];
    }
};

const onFileTypeChange = () => {
    formData.documentCategoryId = null;
    // 清空发放清单
    formData.documents = [];
    fileList.value = [];
};

const onTypeDictNodeIdChange = () => {
    // 清空发放清单
    formData.documents = [];
    // 获取对应的文件列表
    fetchFileList();
};

/**
 * vxe-table
 */
const handleAddFile = () => {
    if (!formData.documentCategoryId) {
        window.$message.warning('请先选择文件类别');
        return;
    }

    formData.documents.push({
        id: Date.now() + Math.random(),
        documentId: '',
        documentNo: '',
        documentVersionNo: ''
    });
};

// 删除文件
const handleRemoveFile = (row: any) => {
    const index = formData.documents?.findIndex((item: any) => item.id === row.id);
    if (index > -1) {
        formData.documents.splice(index, 1);
    }
};

// 文件选项
const documentIdOptions = computed(() => {
    return fileList.value.map((item) => ({
        label: item.name || item.fileName,
        value: item.id,
        data: item
    }));
});

// 文件编号选项
const documentNoOptions = computed(() => {
    return fileList.value.map((item) => ({
        label: item.number || item.originalNumber || '',
        value: item.number || item.originalNumber || '',
        data: item
    }));
});

// 文件选择变化处理
const handleDocumentIdChange = (row: any) => {
    if (!row.documentId) {
        row.documentNo = '';
        row.documentVersionNo = '';
        return;
    }

    // 根据选择的文件ID找到对应的文件数据
    const selectedFile = fileList.value.find((file) => file.id === row.documentId);
    if (selectedFile) {
        // 自动填充文件编号和版本版次
        row.documentNo = selectedFile.number || selectedFile.originalNumber || '';
        row.documentVersionNo = selectedFile.version || selectedFile.originalVersion || '';
    }
};

// 文件编号选择变化处理
const handleDocumentNoChange = (row: any) => {
    if (!row.documentNo) {
        row.documentId = '';
        row.documentVersionNo = '';
        return;
    }

    // 根据选择的文件编号找到对应的文件数据
    const selectedFile = fileList.value.find((file) => (file.number || file.originalNumber) === row.documentNo);
    if (selectedFile) {
        // 自动填充文件ID和版本版次
        row.documentId = selectedFile.id;
        row.documentVersionNo = selectedFile.version || selectedFile.originalVersion || '';
    }
};

// 表格验证
const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    documentId: [{ required: true, message: '请选择文件名称' }],
    documentNo: [{ required: true, message: '请选择文件编号' }],
    documentVersionNo: [{ required: true, message: '版本/版次不能为空' }]
});

// 格式化选项标签 - 将 id 转换为对应的 label 显示
const formatOptionLabel = (_rowId: string, optionType: string, value: string): string => {
    if (!value) return '';

    // 选项类型映射表
    const optionMap = {
        documentIdOptions: documentIdOptions.value,
        documentNoOptions: documentNoOptions.value
    } as const;

    const options = optionMap[optionType as keyof typeof optionMap];
    if (!options) return value;

    const option = options.find((opt) => opt.value === value);
    return option?.label ?? value;
};

// 初始化表单数据
const initFormData = () => {
    if (props.row && props.row.id) {
        // 编辑或详情模式，回显数据
        Object.assign(formData, {
            applicant: props.row.applicant || store.userInfo.nickname || '',
            applyDate: props.row.applyDate || dayjs().valueOf(),
            fileType: props.row.fileType || null,
            documentCategoryId: props.row.documentCategoryId || null,
            reason: props.row.reason || null,
            otherReason: props.row.otherReason || null,
            wishInvalidDate: props.row.wishInvalidDate || null,
            documents:
                props.row.documents && props.row.documents.length > 0
                    ? props.row.documents
                    : [
                          {
                              id: Date.now() + Math.random(),
                              documentId: '',
                              documentNo: '',
                              documentVersionNo: ''
                          }
                      ]
        });

        // 如果有文件类型和类别，获取对应的文件列表
        if (formData.fileType && formData.documentCategoryId) {
            fetchFileList();
        }
    }
};

const onSubmit = async (type: 'submit' | 'saveTemp' = 'submit') => {
    try {
        await formRef.value?.validate();
        await $utils.vxeTableConfig.tableValid(tableRef);
    } catch (err: any) {
        // 用来提示表单必填错误
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }

    const submitData = {
        applicant: formData.applicant || store.userInfo.nickname || '',
        applyDate: formData.applyDate,
        fileType: formData.fileType as unknown as number,
        typeDictNodeId: formData.documentCategoryId as unknown as string,
        reason: formData.reason as unknown as string,
        otherReason: formData.otherReason || '',
        wishDistributeDate: formData.wishInvalidDate as unknown as number,
        documents: formData.documents.map((doc: any) => ({
            documentId: doc.documentId,
            documentNo: doc.documentNo,
            documentVersionNo: doc.documentVersionNo
        }))
    };

    if (props.row && props.row.id) {
        // 编辑模式
        if (type === 'submit') {
            await $apis.nebula.api.v1.invalidApplication.update({
                id: props.row.id,
                ...submitData
            });
        } else {
            await $apis.nebula.api.v1.invalidApplication.saveTemp({
                id: props.row.id,
                ...submitData
            });
        }
    } else {
        // 新增模式
        if (type === 'submit') {
            await $apis.nebula.api.v1.invalidApplication.create(submitData);
        } else {
            await $apis.nebula.api.v1.invalidApplication.saveTemp(submitData);
        }
    }
    window.$message.success(type === 'submit' ? '提交成功' : '暂存成功');
};

// 组件挂载时初始化数据
onMounted(() => {
    initFormData();
});

const formRef = ref();
const tableRef = ref();
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>

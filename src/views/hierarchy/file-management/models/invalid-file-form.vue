<template>
    <alert-content :on-default-save="submit">
        <n-descriptions
            label-placement="left"
            label-align="right"
            label-style="width: 90px;"
            bordered
            :column="2"
            size="small"
        >
            <n-descriptions-item label="申请人"> {{ data.applicant }} </n-descriptions-item>
            <n-descriptions-item label="申请日期"> {{ data.applyDate }} </n-descriptions-item>
            <n-descriptions-item label="文件名称" :span="2"> {{ data.name }} </n-descriptions-item>
            <n-descriptions-item label="文件类型">
                {{ data.type === 2 ? '内部文件' : '外部文件' }}
            </n-descriptions-item>
            <n-descriptions-item label="文件类别"> {{ data.fileCategory }} </n-descriptions-item>
            <n-descriptions-item label="文件编号"> {{ data.number }} </n-descriptions-item>
            <n-descriptions-item label="版本/版次"> {{ data.versionNo }} </n-descriptions-item>
        </n-descriptions>
        <n-form
            ref="formRef"
            class="mt-15px"
            :model="form"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="110"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="拟定作废日期" path="wishInvalidDate" :span="12">
                    <n-date-picker
                        v-model:value="form.wishInvalidDate"
                        type="date"
                        placeholder="请选择拟定作废日期"
                        clearable
                        format="yyyy-MM-dd"
                        class="w-full"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="作废原因" path="reason" :span="12">
                    <n-select
                        v-model:value="form.reason"
                        :options="$datas.invalidFile.reasonOptions"
                        placeholder="请选择作废原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="form.reason === 9" :span="24" label="其他原因" path="otherReason">
                    <n-input v-model:value="form.otherReason" maxlength="50" placeholder="请输入其他原因" show-count />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import { FormRules } from 'naive-ui';

const store = useStore();

const props = defineProps<{
    row: any;
    type: number; // 2-内部文件 3-外部文件
}>();

const formRef = ref();
const data = computed(() => {
    return {
        applicant: store.userInfo.nickname,
        applyDate: dayjs().format('YYYY-MM-DD'),
        name: props.row.name,
        versionNo: props.row.versionNo || props.row.version,
        fileCategory: props.row.docCategoryName || props.row.docType,
        number: props.row.no || props.row.number,
        type: props.type
    };
});

const form = reactive({
    wishInvalidDate: null as number | null,
    reason: null as number | null,
    otherReason: null as string | null
});
const rules: FormRules = {
    wishInvalidDate: { required: true, message: '请选择拟定作废日期', trigger: 'change', type: 'number' },
    reason: { required: true, message: '请选择发放原因', trigger: ['change', 'blur'], type: 'number' },
    otherReason: { required: form?.reason === 9, message: '请输入其他原因', trigger: 'blur' }
};

const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    // 组装数据
    const documents = [
        {
            fileId: props.row.id,
            fileName: props.row.name,
            number: props.row.no,
            version: props.row.versionNo
        }
    ];
    const result = {
        applicant: data.value.applicant,
        applyDate: Date.now(),
        reason: form.reason,
        otherReason: form.otherReason,
        documents
    };
    const formData = JSON.stringify({
        businessId: 'FILE_REPEAL',
        version: '1.0.0',
        data: result
    });
    await $hooks.useApprovalProcess('FILE_REPEAL', formData);
    window.$message.success('作废申请已提交');
};
</script>

<style scoped></style>

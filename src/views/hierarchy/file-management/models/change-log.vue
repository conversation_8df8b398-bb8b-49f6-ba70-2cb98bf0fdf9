<template>
    <div class="change-log">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1200
            }"
            :data-api="$apis.nebula.api.v1[props.type === 2 ? 'internal' : 'external'].log"
            :params="params"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50]
            }"
            :search-props="{ show: false }"
        >
            <template #table_departmentNames="{ row }">
                <span>{{ row.departmentNames?.join(',') }}</span>
                <span v-if="!row.departmentNames || row.departmentNames?.length === 0" class="text-gray-400"> - </span>
            </template>
            <template #table_authorNames="{ row }">
                <span>{{ row.authorNames?.join(',') }}</span>
                <span v-if="!row.authorNames || row.authorNames?.length === 0" class="text-gray-400"> - </span>
            </template>
            <template #table_auditors="{ row }">
                <n-space vertical size="small">
                    <span v-for="item in row.approvalInfo?.auditors" :key="item.id">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </span>
                    <span v-if="!row.approvalInfo || row.approvalInfo?.auditors.length === 0" class="text-gray-400">
                        -
                    </span>
                </n-space>
            </template>
            <template #table_approvers="{ row }">
                <n-space vertical size="small">
                    <span v-for="item in row.approvalInfo?.approvers" :key="item.id">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </span>
                    <span v-if="!row.approvalInfo || row.approvalInfo?.approvers.length === 0" class="text-gray-400">
                        -
                    </span>
                </n-space>
            </template>
            <template #table_publishDate="{ row }">
                <n-time :time="row.publishDate" format="yyyy-MM-dd" />
            </template>
            <template #table_effectiveDate="{ row }">
                <n-time :time="row.effectiveDate" format="yyyy-MM-dd" />
            </template>
            <template #table_operationType="{ row }">
                <n-tag v-bind="getOperationTypeConfig(row.operationType)" round size="small" :bordered="false">
                    {{ getOperationTypeConfig(row.operationType).text }}
                </n-tag>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        id?: string;
        type: number; //  2-内部库 3-外部库
        deptOptions?: any[];
        categoryOptions?: any[];
        bookLibraryOptions?: any[];
        domainOptions?: any[];
        certTypeOptions?: any[];
        orgType?: number;
    }>(),
    {
        id: '',
        type: 2
    }
);

const searchTablePageRef = ref();
const params = ref({
    documentId: props.id
});

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => `${index + 1}`,
        fixed: 'left'
    },
    {
        title: '文件编号',
        key: 'documentNo',
        align: 'center',
        minWidth: 200,
        fixed: 'left',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '版本/版次',
        key: 'documentVersion',
        align: 'center',
        width: 80
    },
    {
        title: '文件名称',
        key: 'documentName',
        align: 'center',
        minWidth: 200,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件类别',
        key: 'documentCategory',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制部门',
        key: 'departmentNames',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制人',
        key: 'authorNames',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审核人',
        key: 'auditors',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '批准人',
        key: 'approvers',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发布日期',
        key: 'publishDate',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '实施日期',
        key: 'effectiveDate',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '操作类型',
        key: 'operationType',
        align: 'center',
        width: 80,
        ellipsis: {
            tooltip: true
        }
    }
]);

// 操作类型配置映射
const getOperationTypeConfig = (operationType: number) => {
    const configMap = {
        0: { text: '无', type: 'default' },
        1: { text: '新增', type: 'success' },
        2: { text: '修订', type: 'primary' },
        3: { text: '作废', type: 'error' }
    } as const;

    return configMap[operationType as keyof typeof configMap] || { text: '未知', type: 'default' };
};
</script>

<style scoped lang="less"></style>
